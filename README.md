# Technexion implementation of camera driver for NXP EVK board  
[![Technexion](https://github.com/user-attachments/assets/c78973b8-4967-4d72-8082-5d6b9ef5dc99)](https://www.technexion.com/products/embedded-vision/)


[![Producer: Technexion](https://img.shields.io/badge/Producer-Technexion-blue.svg)](https://www.technexion.com)
[![License: GPL v2](https://img.shields.io/badge/License-GPL%20v2-blue.svg)](https://www.gnu.org/licenses/old-licenses/gpl-2.0.en.html)

This repository contains camera driver and device tree blobs (DTBs) for [TechNexion Embedded Vision Solutions](https://www.technexion.com/products/embedded-vision/) on NXP EVK board.

Refer to the [TEVS Camera Usage Guide for NXP](https://tn-docusaurus.vercel.app/docs/embedded-vision/tevs/usage-guides/nxp/), this guide offers a comprehensive walkthrough for utilizing TechNexion's TEVS camera modules on the NXP EVK board. It covers essential steps from hardware setup, including connecting the adapter board and camera cable to the MIPI CSI port, to preparing and flashing the Yocto demo image. <br/><br/>
The guide also details the process of configuring the media controller, starting the camera video stream via GStreamer, and troubleshooting common issues. Additionally, it provides instructions for building a Yocto-based Linux image tailored for camera modules, ensuring developers have all the necessary tools and knowledge to accelerate their embedded vision projects. This resource is invaluable for embedded system developers looking to leverage high-performance, industrial-grade camera solutions for applications in industrial automation, prototyping, and edge AI.
