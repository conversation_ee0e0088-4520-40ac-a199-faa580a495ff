From 971805639b796751863cf68f384fab8a3b7c19ec Mon Sep 17 00:00:00 2001
From: <PERSON><PERSON> <<EMAIL>>
Date: Thu, 24 Jul 2025 09:30:18 +0800
Subject: [PATCH 08/10] media: i2c: tevs: fix 64-bit division calculation

---
 drivers/media/i2c/tevs/tevs_main.c | 8 +++++---
 1 file changed, 5 insertions(+), 3 deletions(-)

diff --git a/drivers/media/i2c/tevs/tevs_main.c b/drivers/media/i2c/tevs/tevs_main.c
index dd12e4c32737..4bf9972b26d9 100644
--- a/drivers/media/i2c/tevs/tevs_main.c
+++ b/drivers/media/i2c/tevs/tevs_main.c
@@ -1830,7 +1830,7 @@ static int tevs_ctrls_init(struct tevs *tevs)
 			V4L2_CID_VBLANK, 0, 0, 1, 0);
 
 	/* By default, link_freq and pixel_rate is read only */
-	link_freq[0] = (tevs->data_frequency / 2) * 1000000;
+	link_freq[0] = div_u64((tevs->data_frequency >> 1), 1000000ULL);
 	tevs->link_freq = v4l2_ctrl_new_int_menu(
 		ctrl_hdlr, &tevs_ctrl_ops, V4L2_CID_LINK_FREQ,
 		ARRAY_SIZE(link_freq) - 1, 0, link_freq);
@@ -2126,9 +2126,11 @@ static int tevs_check_hwcfg(struct device *dev, struct tevs *tevs)
 
 	/* Check the link frequency set in device tree */
 	if (ep_cfg.nr_of_link_frequencies == 0)
-		tevs->data_frequency = (TEVS_LINK_FREQUENCY_DEFAULT / 1000000) * 2;
+		tevs->data_frequency = div_u64(((u64)TEVS_LINK_FREQUENCY_DEFAULT * 2),
+							1000000ULL);
 	else if (ep_cfg.nr_of_link_frequencies == 1)
-		tevs->data_frequency = (ep_cfg.link_frequencies[0] / 1000000) * 2;
+		tevs->data_frequency = div_u64((ep_cfg.link_frequencies[0] * 2),
+							1000000ULL);
 	else {
 		dev_err(dev, "invalid link frequencies %u on port\n",
 				ep_cfg.nr_of_link_frequencies);
-- 
2.25.1

