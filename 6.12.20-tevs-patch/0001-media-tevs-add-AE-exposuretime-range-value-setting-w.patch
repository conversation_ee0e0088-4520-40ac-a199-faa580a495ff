From ce4334d626378c2931c396f5cdf6f9ba02e35b14 Mon Sep 17 00:00:00 2001
From: <PERSON><PERSON> <<EMAIL>>
Date: Fri, 18 Apr 2025 13:42:31 +0800
Subject: [PATCH 01/10] media: tevs: add AE exposuretime range value setting
 when starting streaming

---
 drivers/media/i2c/tevs/tevs_main.c | 6 ++++++
 1 file changed, 6 insertions(+)

diff --git a/drivers/media/i2c/tevs/tevs_main.c b/drivers/media/i2c/tevs/tevs_main.c
index 1edcaa5f575d..31b3eb5341f7 100644
--- a/drivers/media/i2c/tevs/tevs_main.c
+++ b/drivers/media/i2c/tevs/tevs_main.c
@@ -668,6 +668,12 @@ static int tevs_set_stream(struct v4l2_subdev *sub_dev, int enable)
 			tevs_i2c_read(tevs, TEVS_AE_MANUAL_EXP_TIME, exp, 4);
 			tevs->exp_time->cur.val = be32_to_cpup((__be32 *)exp) &
 						  TEVS_AE_MANUAL_EXP_TIME_MASK;
+			tevs_i2c_read(tevs, TEVS_AE_AUTO_EXP_TIME_UPPER, exp, 4);
+			tevs->ae_exp_upper->cur.val = be32_to_cpup((__be32 *)exp) &
+						  TEVS_AE_MANUAL_EXP_TIME_MASK;
+			tevs_i2c_read(tevs, TEVS_AE_AUTO_EXP_TIME_MAX, exp, 4);
+			tevs->ae_exp_max->cur.val = be32_to_cpup((__be32 *)exp) &
+						  TEVS_AE_MANUAL_EXP_TIME_MASK;
 		}
 	}
 
-- 
2.25.1

