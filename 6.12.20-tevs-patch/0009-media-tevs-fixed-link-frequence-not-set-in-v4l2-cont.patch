From 177336f8cd0a3da6351b43f1235a3667e640ca07 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Fri, 1 Aug 2025 00:19:27 -0700
Subject: [PATCH 09/10] media: tevs: fixed link frequence not set in v4l2
 control list.

---
 drivers/media/i2c/tevs/tevs_main.c | 2 +-
 1 file changed, 1 insertion(+), 1 deletion(-)

diff --git a/drivers/media/i2c/tevs/tevs_main.c b/drivers/media/i2c/tevs/tevs_main.c
index 4bf9972b26d9..65a0fe43b522 100644
--- a/drivers/media/i2c/tevs/tevs_main.c
+++ b/drivers/media/i2c/tevs/tevs_main.c
@@ -1830,7 +1830,7 @@ static int tevs_ctrls_init(struct tevs *tevs)
 			V4L2_CID_VBLANK, 0, 0, 1, 0);
 
 	/* By default, link_freq and pixel_rate is read only */
-	link_freq[0] = div_u64((tevs->data_frequency >> 1), 1000000ULL);
+	link_freq[0] = (u64)(tevs->data_frequency >> 1) * 1000000ULL;
 	tevs->link_freq = v4l2_ctrl_new_int_menu(
 		ctrl_hdlr, &tevs_ctrl_ops, V4L2_CID_LINK_FREQ,
 		ARRAY_SIZE(link_freq) - 1, 0, link_freq);
-- 
2.25.1

