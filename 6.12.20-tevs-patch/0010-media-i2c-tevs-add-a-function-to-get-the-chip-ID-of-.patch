From 4a10faeee78854ed8a30d0958a458f87708d0b08 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Mon, 18 Aug 2025 15:01:50 +0800
Subject: [PATCH 10/10] media: i2c: tevs: add a function to get the chip ID of
 a sensor and check if the chip ID is supported by the driver

---
 drivers/media/i2c/tevs/tevs_main.c | 71 ++++++++++++++++++++++--------
 drivers/media/i2c/tevs/tevs_tbls.h | 35 +++++++++++----
 2 files changed, 80 insertions(+), 26 deletions(-)

diff --git a/drivers/media/i2c/tevs/tevs_main.c b/drivers/media/i2c/tevs/tevs_main.c
index 65a0fe43b522..6c8f2ff2ebb7 100644
--- a/drivers/media/i2c/tevs/tevs_main.c
+++ b/drivers/media/i2c/tevs/tevs_main.c
@@ -23,6 +23,10 @@
 #define HOST_COMMAND_TEVS_INFO_VERSION_MSB 						(0x3000)
 #define HOST_COMMAND_TEVS_INFO_VERSION_LSB 						(0x3002)
 #define HOST_COMMAND_TEVS_BOOT_STATE 							(0x3004)
+#define HOST_COMMAND_TEVS_SENSOR_CHIP_ID                        (0x3008)
+#define HOST_COMMAND_TEVS_MODEL_NUMBER_0                        (0x300C)
+#define HOST_COMMAND_TEVS_MODEL_NUMBER_1                        (0x300E)
+#define HOST_COMMAND_TEVS_MODEL_NUMBER_2                        (0x3010)
 
 /* Define host command register of ISP control page */
 #define HOST_COMMAND_ISP_CTRL_PREVIEW_WIDTH 					(0x3100)
@@ -334,6 +338,7 @@ struct tevs {
 	struct gpio_desc *host_pwdn_gpio;
 	struct gpio_desc *standby_gpio;
 
+    u16 chip_id;
 	int data_lanes;
 	int continuous_clock;
 	int data_frequency;
@@ -445,6 +450,22 @@ static int tevs_i2c_write_16b(struct tevs *tevs, u16 reg, u16 val)
 	return 0;
 }
 
+static int tevs_get_chip_id(struct tevs *tevs)
+{
+	struct i2c_client *client = v4l2_get_subdevdata(&tevs->v4l2_subdev);
+    u16 val;
+    int ret = tevs_i2c_read_16b(tevs, HOST_COMMAND_TEVS_SENSOR_CHIP_ID, &val);
+
+    if (ret < 0) {
+        dev_err(&client->dev, "Can't get chip ID. ret = %d.\n", ret);
+		return ret;
+	}
+
+    tevs->chip_id = val;
+    dev_info(&client->dev, "Chip ID: 0x%.4X\n", tevs->chip_id);
+	return 0;
+}
+
 static int tevs_check_trigger_mode(struct tevs *tevs)
 {
 	struct i2c_client *client = v4l2_get_subdevdata(&tevs->v4l2_subdev);
@@ -2229,30 +2250,44 @@ static int tevs_probe(struct i2c_client *client)
 	if (ret < 0) {
 		dev_err(dev, "load header information failed\n");
 		goto error_power_off;
-	} else {
-		for (i = 0; i < ARRAY_SIZE(tevs_sensor_table); i++) {
-			if (strcmp((const char *)tevs->header_info->product_name,
-				   tevs_sensor_table[i].sensor_name) == 0)
-				break;
-		}
-	}
-
-	if (i >= ARRAY_SIZE(tevs_sensor_table)) {
-		dev_err(dev, "cannot not support the product: %s\n",
-			(const char *)tevs->header_info->product_name);
-		ret = -EINVAL;
-		goto error_power_off;
 	}
 
-	tevs->selected_sensor = i;
-	dev_dbg(dev, "selected_sensor:%d, sensor_name:%s\n", i,
-		tevs->header_info->product_name);
+    ret = tevs_get_chip_id(tevs);
+
+    if (ret < 0) {
+        dev_err(dev, "get chip ID failed\n");
+        goto error_power_off;
+    }
+    
+    if (tevs->chip_id == SENSOR_CHIP_ID_NONE) {
+        for (i = 0; i < ARRAY_SIZE(tevs_sensor_table); i++) {
+            if (strcmp((const char *)tevs->header_info->product_name, tevs_sensor_table[i].sensor_name) == 0)
+                break;
+        }
+    } else {
+        for (i = 0; i < ARRAY_SIZE(tevs_sensor_table); i++) {
+            if (tevs->chip_id == tevs_sensor_table[i].chip_id)
+                break;
+        }
+    }
+
+    if (i >= ARRAY_SIZE(tevs_sensor_table)) {
+        if (tevs->chip_id == SENSOR_CHIP_ID_NONE)
+            dev_err(dev, "cannot not support the product: %s\n", (const char *)tevs->header_info->product_name);
+        else
+            dev_err(dev, "cannot not support the chip ID: 0x%.4X\n", tevs->chip_id);
+
+        ret = -EINVAL;
+        goto error_power_off;
+    }
+
+    tevs->selected_sensor = i;
+	dev_dbg(dev, "selected_sensor:%d, sensor_name:%s\n", i, tevs->header_info->product_name);
 
 	/* Initialize default format */
 	fmt = &tevs->fmt;
 	fmt->width = tevs_sensor_table[tevs->selected_sensor].res_list[0].width;
-	fmt->height =
-		tevs_sensor_table[tevs->selected_sensor].res_list[0].height;
+	fmt->height = tevs_sensor_table[tevs->selected_sensor].res_list[0].height;
 	fmt->field = V4L2_FIELD_NONE;
 	fmt->code = tevs_sensor_table[tevs->selected_sensor].code_list[0];
 	fmt->colorspace = V4L2_COLORSPACE_SRGB;
diff --git a/drivers/media/i2c/tevs/tevs_tbls.h b/drivers/media/i2c/tevs/tevs_tbls.h
index 40409e6d5385..9b037ce2f179 100644
--- a/drivers/media/i2c/tevs/tevs_tbls.h
+++ b/drivers/media/i2c/tevs/tevs_tbls.h
@@ -3,6 +3,16 @@
 #include <linux/kernel.h>
 #include <media/v4l2-subdev.h>
 
+#define SENSOR_CHIP_ID_NONE                 0x0000
+#define SENSOR_CHIP_ID_ONSEMI_AR0144        0x0356
+#define SENSOR_CHIP_ID_ONSEMI_AR0145        0x1750
+#define SENSOR_CHIP_ID_ONSEMI_AR0234        0x0A56
+#define SENSOR_CHIP_ID_ONSEMI_AR0521        0x0457
+#define SENSOR_CHIP_ID_ONSEMI_AR0522        0x1457
+#define SENSOR_CHIP_ID_ONSEMI_AR0821        0x2557
+#define SENSOR_CHIP_ID_ONSEMI_AR0822        0x0F56
+#define SENSOR_CHIP_ID_ONSEMI_AR1335        0x0153
+
 struct resolution {
 	u16 width;
 	u16 height;
@@ -125,6 +135,7 @@ static u32 ar1335_code_list[] = {
 };
 
 struct sensor_info {
+    const u16 chip_id;
 	const char *sensor_name;
 	struct resolution *res_list;
 	u32 res_list_size;
@@ -133,42 +144,50 @@ struct sensor_info {
 };
 
 static struct sensor_info tevs_sensor_table[] = {
-	{ .sensor_name = "TEVS-AR0144",
+	{ .chip_id = SENSOR_CHIP_ID_ONSEMI_AR0144,
+      .sensor_name = "TEVS-AR0144",
 	  .res_list = ar0144_res_list,
 	  .res_list_size = ARRAY_SIZE(ar0144_res_list),
 	  .code_list = ar0144_code_list,
 	  .code_list_size = ARRAY_SIZE(ar0144_code_list) },
-	{ .sensor_name = "TEVS-AR0145",
+	{ .chip_id = SENSOR_CHIP_ID_ONSEMI_AR0145,
+      .sensor_name = "TEVS-AR0145",
 	  .res_list = ar0145_res_list,
 	  .res_list_size = ARRAY_SIZE(ar0145_res_list),
 	  .code_list = ar0145_code_list,
 	  .code_list_size = ARRAY_SIZE(ar0145_code_list) },
-	{ .sensor_name = "TEVS-AR0234",
+	{ .chip_id = SENSOR_CHIP_ID_ONSEMI_AR0234,
+      .sensor_name = "TEVS-AR0234",
 	  .res_list = ar0234_res_list,
 	  .res_list_size = ARRAY_SIZE(ar0234_res_list),
 	  .code_list = ar0234_code_list,
 	  .code_list_size = ARRAY_SIZE(ar0234_code_list) },
-	{ .sensor_name = "TEVS-AR0521",
+	{ .chip_id = SENSOR_CHIP_ID_ONSEMI_AR0521,
+      .sensor_name = "TEVS-AR0521",
 	  .res_list = ar0521_res_list,
 	  .res_list_size = ARRAY_SIZE(ar0521_res_list),
 	  .code_list = ar0521_code_list,
 	  .code_list_size = ARRAY_SIZE(ar0521_code_list) },
-	{ .sensor_name = "TEVS-AR0522",
+	{ .chip_id = SENSOR_CHIP_ID_ONSEMI_AR0522,
+      .sensor_name = "TEVS-AR0522",
 	  .res_list = ar0522_res_list,
 	  .res_list_size = ARRAY_SIZE(ar0522_res_list),
 	  .code_list = ar0522_code_list,
 	  .code_list_size = ARRAY_SIZE(ar0522_code_list) },
-	{ .sensor_name = "TEVS-AR0821",
+	{ .chip_id = SENSOR_CHIP_ID_ONSEMI_AR0821,
+      .sensor_name = "TEVS-AR0821",
 	  .res_list = ar0821_res_list,
 	  .res_list_size = ARRAY_SIZE(ar0821_res_list),
 	  .code_list = ar0821_code_list,
 	  .code_list_size = ARRAY_SIZE(ar0821_code_list) },
-	{ .sensor_name = "TEVS-AR0822",
+	{ .chip_id = SENSOR_CHIP_ID_ONSEMI_AR0822,
+      .sensor_name = "TEVS-AR0822",
 	  .res_list = ar0822_res_list,
 	  .res_list_size = ARRAY_SIZE(ar0822_res_list),
 	  .code_list = ar0822_code_list,
 	  .code_list_size = ARRAY_SIZE(ar0822_code_list) },
-	{ .sensor_name = "TEVS-AR1335",
+	{ .chip_id = SENSOR_CHIP_ID_ONSEMI_AR1335,
+      .sensor_name = "TEVS-AR1335",
 	  .res_list = ar1335_res_list,
 	  .res_list_size = ARRAY_SIZE(ar1335_res_list),
 	  .code_list = ar1335_code_list,
-- 
2.25.1

